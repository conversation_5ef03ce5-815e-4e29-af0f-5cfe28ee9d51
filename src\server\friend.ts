"use server";

import { db } from "@/db";
import { friend, user } from "@/db/schema";
import { and, eq, or, ilike, ne } from "drizzle-orm";
import { redis } from "@/lib/redis";

const getCacheKey = (prefix: string, userId: string) => `friend-${prefix}:${userId}`;

export const searchUsers = async (
  query: string,
  currentUserId: string
): Promise<
  {
    id: string;
    name: string;
    email: string;
    image: string | null;
    bio: string | null;
  }[]
> => {
  const cacheKey = getCacheKey("search", `${currentUserId}:${query}`);

  try {
    const cached = await redis.get(cacheKey);
    if (cached) return JSON.parse(cached);
  } catch (error) {
    console.error("Redis get error:", error);
  }

  const results = await db
    .select({
      id: user.id,
      name: user.name,
      email: user.email,
      image: user.image,
      bio: user.bio,
    })
    .from(user)
    .where(
      and(
        or(
          eq(user.id, query), // Exact ID match
          ilike(user.name, `%${query}%`), // Name contains query
          ilike(user.email, `%${query}%`) // Email contains query
        ),
        ne(user.id, currentUserId) // Exclude current user
      )
    )
    .limit(10);

  const filteredResults = results.filter((u) => u.id !== currentUserId);

  try {
    await redis.set(cacheKey, JSON.stringify(filteredResults), { EX: Number(process.env.CACHE_TTL) });
  } catch (error) {
    console.error("Redis set error:", error);
  }

  return filteredResults;
};

export const sendRequest = async (sender: string, receiver: string): Promise<void> => {
  await db.insert(friend).values({ sender, receiver, status: false });
  try {
    await Promise.all([
      redis.del(getCacheKey("friends", sender)),
      redis.del(getCacheKey("friends", receiver)),
      redis.del(getCacheKey("requests_for_me", receiver)),
      redis.del(getCacheKey("requests_from_me", sender)),
    ]);
  } catch (error) {
    console.error("Redis cache invalidation error:", error);
  }
};

export const acceptRequest = async (sender: string, receiver: string): Promise<void> => {
  await db
    .update(friend)
    .set({ status: true })
    .where(and(eq(friend.sender, sender), eq(friend.receiver, receiver)));
  try {
    await Promise.all([
      redis.del(getCacheKey("friends", sender)),
      redis.del(getCacheKey("friends", receiver)),
      redis.del(getCacheKey("requests_for_me", receiver)),
      redis.del(getCacheKey("requests_from_me", sender)),
    ]);
  } catch (error) {
    console.error("Redis cache invalidation error:", error);
  }
};

export const denyRequest = async (sender: string, receiver: string): Promise<void> => {
  await db.delete(friend).where(and(eq(friend.sender, sender), eq(friend.receiver, receiver)));
  try {
    await Promise.all([
      redis.del(getCacheKey("friends", sender)),
      redis.del(getCacheKey("friends", receiver)),
      redis.del(getCacheKey("requests_for_me", receiver)),
      redis.del(getCacheKey("requests_from_me", sender)),
    ]);
  } catch (error) {
    console.error("Redis cache invalidation error:", error);
  }
};

export const getFromMeRequests = async (
  userId: string
): Promise<
  {
    id: string;
    name: string;
    image: string | null;
    bio: string | null;
    sender: string;
    receiver: string;
  }[]
> => {
  const cacheKey = getCacheKey("requests_from_me", userId);

  try {
    const cached = await redis.get(cacheKey);
    if (cached) return JSON.parse(cached);
  } catch (error) {
    console.error("Redis get error:", error);
  }

  const results = await db
    .select({
      id: user.id,
      name: user.name,
      image: user.image,
      bio: user.bio,
      sender: friend.sender,
      receiver: friend.receiver,
    })
    .from(friend)
    .innerJoin(user, eq(friend.receiver, user.id))
    .where(and(eq(friend.status, false), eq(friend.sender, userId)));

  try {
    await redis.set(cacheKey, JSON.stringify(results), { EX: Number(process.env.CACHE_TTL) });
  } catch (error) {
    console.error("Redis set error:", error);
  }

  return results;
};

export const getForMeRequests = async (
  userId: string
): Promise<
  {
    id: string;
    name: string;
    image: string | null;
    bio: string | null;
    sender: string;
    receiver: string;
  }[]
> => {
  const cacheKey = getCacheKey("requests_for_me", userId);

  try {
    const cached = await redis.get(cacheKey);
    if (cached) {
      return JSON.parse(cached);
    }
  } catch (error) {
    console.error("Redis get error:", error);
  }

  const results = await db
    .select({
      id: user.id,
      name: user.name,
      image: user.image,
      bio: user.bio,
      sender: friend.sender,
      receiver: friend.receiver,
    })
    .from(friend)
    .innerJoin(user, eq(friend.sender, user.id))
    .where(and(eq(friend.status, false), eq(friend.receiver, userId)));

  try {
    await redis.set(cacheKey, JSON.stringify(results), { EX: Number(process.env.CACHE_TTL) });
  } catch (error) {
    console.error("Redis set error:", error);
  }
  return results;
};

export const getFriends = async (
  userId: string
): Promise<
  {
    id: string;
    name: string;
    image: string | null;
    bio: string | null;
    sender: string;
    receiver: string;
  }[]
> => {
  const cacheKey = getCacheKey("friends", userId);

  try {
    const cached = await redis.get(cacheKey);
    if (cached) return JSON.parse(cached);
  } catch (error) {
    console.error("Redis get error:", error);
  }

  const results = await db
    .select({
      id: user.id,
      name: user.name,
      image: user.image,
      bio: user.bio,
      sender: friend.sender,
      receiver: friend.receiver,
    })
    .from(friend)
    .innerJoin(user, or(eq(friend.sender, user.id), eq(friend.receiver, user.id)))
    .where(and(eq(friend.status, true), or(eq(friend.sender, userId), eq(friend.receiver, userId))))
    .then((friends) => friends.filter((f) => f.id !== userId));

  try {
    await redis.set(cacheKey, JSON.stringify(results), { EX: Number(process.env.CACHE_TTL) });
  } catch (error) {
    console.error("Redis set error:", error);
  }

  return results;
};
